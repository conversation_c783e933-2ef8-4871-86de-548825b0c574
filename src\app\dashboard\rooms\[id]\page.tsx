'use client';

import { useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Building,
  Users,
  MapPin,
  Clock,
  ArrowLeft,
  Wifi,
  Monitor,
  Coffee,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import { useMeetingRoom, useMeetingRooms } from '@/lib/hooks/use-meeting-rooms';
import { useCreateBooking, useCancelBooking } from '@/lib/hooks/use-bookings';
import { RoomBookingCalendar } from '@/components/calendar/RoomBookingCalendar';
import { CreateBookingFormData } from '@/components/calendar/types';

export default function MeetingRoomDetailPage() {
  const params = useParams();
  const roomId = params.id as string;

  const { data: room, isLoading: loadingRoom, error } = useMeetingRoom(roomId);
  const { data: meetingRooms } = useMeetingRooms();
  const createBookingMutation = useCreateBooking();
  const cancelBookingMutation = useCancelBooking();

  // Helper function to check if room has amenities or equipment
  const hasAmenitiesOrEquipment = () => {
    const amenities = room?.data?.amenities;
    const equipment = room?.data?.equipment;
    return (Array.isArray(amenities) && amenities.length > 0) ||
      (Array.isArray(equipment) && equipment.length > 0);
  };

  // Helper function to safely access location data
  const getLocationData = () => {
    const location = room?.data?.location;
    if (typeof location === 'object' && location !== null) {
      return location as { building?: string; floor?: string; room?: string };
    }
    return null;
  };

  // Helper function to safely access availability data
  const getAvailabilityData = () => {
    const availability = room?.data?.availability;
    if (typeof availability === 'object' && availability !== null) {
      return availability as {
        businessHours?: { start: string; end: string };
        workingDays?: number[];
        timezone?: string;
      };
    }
    return null;
  };

  // Helper function to safely access booking rules data
  const getBookingRulesData = () => {
    const bookingRules = room?.data?.bookingRules;
    if (typeof bookingRules === 'object' && bookingRules !== null) {
      return bookingRules as {
        minBookingDuration?: number;
        maxBookingDuration?: number;
        advanceBookingDays?: number;
        minNoticeHours?: number;
      };
    }
    return null;
  };

  // Handle booking creation
  const handleCreateBooking = async (data: CreateBookingFormData) => {
    try {
      await createBookingMutation.mutateAsync({
        resourceType: 'MEETING_ROOM',
        resourceId: roomId,
        title: data.title,
        description: data.description,
        startDate: data.startTime,
        endDate: data.endTime,
        attendees: {
          internal: data.internalAttendees || [],
          external: data.externalAttendees || []
        }
      });
    } catch (error) {
      throw error;
    }
  };

  // Handle booking cancellation
  const handleCancelBooking = async (booking: any) => {
    try {
      await cancelBookingMutation.mutateAsync(booking.id);
    } catch (error) {
      throw error;
    }
  };

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi':
        return <Wifi className="h-4 w-4" />;
      case 'projector':
      case 'tv':
      case 'monitor':
        return <Monitor className="h-4 w-4" />;
      case 'coffee':
        return <Coffee className="h-4 w-4" />;
      default:
        return <Building className="h-4 w-4" />;
    }
  };



  if (error) {
    return (
      <div className="flex flex-col gap-6 p-6">
        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-lg font-semibold">Sala no encontrada</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            La sala de reuniones que buscas no existe.
          </p>
          <Button className="mt-4" asChild>
            <Link href="/dashboard/rooms">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Volver a Salas
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  if (loadingRoom) {
    return (
      <div className="flex flex-col gap-6 p-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2 space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/dashboard/rooms">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight">{room?.data?.name}</h1>
            <Badge variant={room?.data?.isActive ? "default" : "secondary"}>
              {room?.data?.isActive ? "Activa" : "Inactiva"}
            </Badge>
          </div>
          <div className="flex items-center gap-2 text-muted-foreground">
            <MapPin className="h-4 w-4" />
            <span>
              {getLocationData()?.building && `${getLocationData()?.building}, `}
              Piso {getLocationData()?.floor}, {getLocationData()?.room}
            </span>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Main Content - Columna izquierda más ancha */}
        <div className="md:col-span-2">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Información General</TabsTrigger>
              <TabsTrigger value="availability">Disponibilidad</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Description */}
              {room?.data?.description && (
                <Card>
                  <CardHeader>
                    <CardTitle>Descripción</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{room.data.description}</p>
                  </CardContent>
                </Card>
              )}

              {/* Amenities & Equipment */}
              <div className="grid gap-6 md:grid-cols-2">
                {Array.isArray(room?.data?.amenities) && room.data.amenities.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Comodidades</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-2">
                        {room.data.amenities.map((amenity: string) => (
                          <div key={amenity} className="flex items-center gap-2">
                            {getAmenityIcon(amenity)}
                            <span className="text-sm">{amenity}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {Array.isArray(room?.data?.equipment) && room.data.equipment.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Equipamiento</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-2">
                        {room.data.equipment.map((item: string) => (
                          <div key={item} className="flex items-center gap-2">
                            <Monitor className="h-4 w-4" />
                            <span className="text-sm">{item}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Booking Rules - Siempre en la columna principal */}
              {getBookingRulesData() && (
                <Card>
                  <CardHeader>
                    <CardTitle>Reglas de Reserva</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      {getBookingRulesData()?.minBookingDuration && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Duración Mínima</span>
                          <span className="text-sm font-medium">
                            {getBookingRulesData()?.minBookingDuration} minutos
                          </span>
                        </div>
                      )}
                      {getBookingRulesData()?.maxBookingDuration && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Duración Máxima</span>
                          <span className="text-sm font-medium">
                            {getBookingRulesData()?.maxBookingDuration} minutos
                          </span>
                        </div>
                      )}
                      {getBookingRulesData()?.advanceBookingDays && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Reserva Anticipada</span>
                          <span className="text-sm font-medium">
                            {getBookingRulesData()?.advanceBookingDays} días
                          </span>
                        </div>
                      )}
                      {getBookingRulesData()?.minNoticeHours && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Aviso Mínimo</span>
                          <span className="text-sm font-medium">
                            {getBookingRulesData()?.minNoticeHours} horas
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Calendar - Siempre en la columna principal */}
              <div id='calendar'>
                <RoomBookingCalendar
                  roomId={roomId}
                  meetingRooms={meetingRooms?.data || []}
                  onCreateBooking={handleCreateBooking}
                  onCancelBooking={handleCancelBooking}
                  isCreatingBooking={createBookingMutation.isPending}
                  isCancellingBooking={cancelBookingMutation.isPending}
                />
              </div>
            </TabsContent>

            <TabsContent value="availability" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Horario de Trabajo</CardTitle>
                </CardHeader>
                <CardContent>
                  {getAvailabilityData()?.businessHours ? (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {getAvailabilityData()?.businessHours?.start} - {getAvailabilityData()?.businessHours?.end}
                      </span>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No hay horario específico configurado</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Días Laborales</CardTitle>
                </CardHeader>
                <CardContent>
                  {getAvailabilityData()?.workingDays ? (
                    <div className="grid grid-cols-7 gap-2">
                      {['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'].map((day, index) => {
                        const isWorkingDay = getAvailabilityData()?.workingDays?.includes(index + 1) || false;
                        return (
                          <div
                            key={day}
                            className={`text-center p-2 rounded text-sm ${isWorkingDay
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-500'
                              }`}
                          >
                            {isWorkingDay ? (
                              <CheckCircle className="h-4 w-4 mx-auto mb-1" />
                            ) : (
                              <XCircle className="h-4 w-4 mx-auto mb-1" />
                            )}
                            {day}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Disponible todos los días</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar - Columna derecha más pequeña - SIEMPRE se muestra */}
        <div className="md:col-span-1">
          {/* Quick Info - Siempre se muestra */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Información Rápida</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Capacidad: {room?.data?.capacity} personas</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Piso {getLocationData()?.floor}, {getLocationData()?.room}
                </span>
              </div>
              {getAvailabilityData()?.timezone && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Zona Horaria: {getAvailabilityData()?.timezone}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Amenities - Solo si existen */}
          {getAmenitiesData() && getAmenitiesData().length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Comodidades</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2">
                  {getAmenitiesData().map((amenity: string, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-purple-600 rounded-full" />
                      <span className="text-sm">{amenity}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Equipment - Solo si existe */}
          {getEquipmentData() && getEquipmentData().length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Equipamiento</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-2">
                  {getEquipmentData().map((item: string, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-purple-600 rounded-full" />
                      <span className="text-sm">{item}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
